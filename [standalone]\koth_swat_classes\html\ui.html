<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>KOTH Class & Shop</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { width: 100vw; height: 100vh; background: rgba(0,0,0,0.8); display: none; font-family: Arial, sans-serif; }
    .container { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80vw; height: 70vh; background: #1f1f1f; border: 3px solid #fff; border-radius: 10px; color: white; padding: 20px; }
    .title { text-align: center; font-size: 2rem; margin-bottom: 20px; color: #0f0; }
    .class-grid { display: flex; justify-content: space-around; height: 80%; }
    .class-card { width: 18%; background: #333; border: 2px solid #555; border-radius: 8px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s; }
    .class-card:hover { border-color: #0f0; transform: scale(1.05); }
    .class-card h3 { font-size: 1.5rem; margin-bottom: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">🎯 SELECT YOUR CLASS</h1>
    <div class="class-grid">
      <div class="class-card" data-class="assault">
        <h3>🔫 Assault</h3>
        <p>Standard combat specialist</p>
        <p>Level 0 Required</p>
      </div>
      <div class="class-card" data-class="medic">
        <h3>🏥 Medic</h3>
        <p>Field medical support</p>
        <p>Level 5 Required</p>
      </div>
      <div class="class-card" data-class="engineer">
        <h3>🔧 Engineer</h3>
        <p>Explosives expert</p>
        <p>Level 15 Required</p>
      </div>
      <div class="class-card" data-class="heavy">
        <h3>💪 Heavy</h3>
        <p>Heavy weapons specialist</p>
        <p>Level 25 Required</p>
      </div>
      <div class="class-card" data-class="scout">
        <h3>🎯 Scout</h3>
        <p>Reconnaissance sniper</p>
        <p>Level 45 Required</p>
      </div>
    </div>
  </div>

  <script>
    console.log('UI loaded');
    window.addEventListener('message', function(event) {
      console.log('Message received:', event.data);
      if (event.data.action === 'show') {
        document.body.style.display = 'block';
      } else if (event.data.action === 'hide') {
        document.body.style.display = 'none';
      }
    });

    document.querySelectorAll('.class-card').forEach(card => {
      card.addEventListener('click', function() {
        const className = this.dataset.class;
        console.log('Class selected:', className);
        fetch(`https://${window.GetParentResourceName ? GetParentResourceName() : 'koth_swat_classes'}/selectClass`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ class: className })
        });
      });
    });
  </script>
</body>
</html>
