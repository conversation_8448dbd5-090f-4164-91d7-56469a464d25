window.addEventListener('message', function (event) {
  if (event.data.action === "show") {
    document.body.style.display = "block";
    const container = document.getElementById("vehicle-grid");
    container.innerHTML = "";

    event.data.vehicles.forEach((v, index) => {
      const card = document.createElement("div");
      card.className = v.locked ? "vehicle-card locked" : "vehicle-card";
      card.style.animationDelay = `${index * 0.1}s`;

      const formattedPrice = v.price.toLocaleString();
      const statusIcon = v.locked ? '🔒' : '✅';
      const statusText = v.locked ? 'LOCKED' : 'AVAILABLE';
      const buttonText = v.locked ? 'REQUIRES RANK' : 'PURCHASE';
      const buttonClass = v.locked ? 'locked-btn' : 'purchase-btn';

      card.innerHTML = `
        <div class="vehicle-image" data-vehicle="${v.model}"></div>
        <div class="vehicle-name">${v.name.toUpperCase()}</div>
        <div class="vehicle-price">${formattedPrice}</div>
        <div class="vehicle-status ${v.locked ? 'status-locked' : 'status-available'}">
          ${statusIcon} ${statusText}
        </div>
        <div class="${buttonClass}" ${!v.locked ? 'onclick="purchaseVehicle(\'' + v.model + '\', ' + v.price + ')"' : ''}>
          ${buttonText}
        </div>
      `;

      container.appendChild(card);
    });
  } else if (event.data.action === "hide") {
    document.body.style.display = "none";
  }
});

function purchaseVehicle(model, price) {
  // Add purchase animation
  const button = event.target;
  button.style.transform = 'scale(0.95)';
  button.innerHTML = 'PURCHASING...';

  setTimeout(() => {
    fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ model: model, price: price })
    });
  }, 300);
}

function closeMenu() {
  fetch(`https://${GetParentResourceName()}/close`, {
    method: "POST"
  });
}

// Test function to show the UI (for development/preview only)
// Uncomment the lines below to test the UI in browser
/*
function showTestUI() {
  const testVehicles = [
    { name: "Elegy", model: "elegy2", price: 200, locked: false },
    { name: "Zentorno", model: "zentorno", price: 1500, locked: false },
    { name: "Adder", model: "adder", price: 2500, locked: false },
    { name: "Insurgent", model = "insurgent", price: 3000, locked: true },
    { name: "Kuruma", model: "kuruma2", price: 3500, locked: true },
    { name: "T20", model: "t20", price: 4000, locked: true },
    { name: "Buzzard", model: "buzzard2", price: 5000, locked: true }
  ];

  window.dispatchEvent(new MessageEvent('message', {
    data: { action: "show", vehicles: testVehicles }
  }));
}

setTimeout(showTestUI, 500);
*/
