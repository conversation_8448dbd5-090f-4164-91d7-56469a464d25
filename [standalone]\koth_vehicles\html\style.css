@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Exo 2', 'Segoe UI', sans-serif;
  background: transparent;
  display: none;
  height: 100vh;
  overflow: hidden;
  color: #ffffff;
}

.garage-container {
  display: flex;
  height: 90vh;
  width: 95vw;
  background: linear-gradient(135deg,
    rgba(15, 15, 35, 0.95) 0%,
    rgba(25, 25, 55, 0.9) 50%,
    rgba(35, 35, 75, 0.85) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 100px rgba(100, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.9);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: blur(0);
  }
}

.sidebar {
  width: 280px;
  background: linear-gradient(180deg,
    rgba(20, 20, 40, 0.9) 0%,
    rgba(15, 15, 35, 0.95) 100%);
  backdrop-filter: blur(15px);
  color: white;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(100, 200, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(100, 200, 255, 0.8) 50%,
    transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.garage-header {
  padding: 40px 30px;
  background: linear-gradient(135deg,
    rgba(100, 200, 255, 0.15) 0%,
    rgba(50, 150, 255, 0.1) 50%,
    rgba(0, 100, 255, 0.05) 100%);
  text-align: center;
  color: #ffffff;
  border-bottom: 1px solid rgba(100, 200, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.header-text {
  font-family: 'Orbitron', monospace;
  font-size: 28px;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 4px;
  text-shadow:
    0 0 15px rgba(100, 200, 255, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 5px;
}

.header-subtext {
  font-family: 'Exo 2', sans-serif;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 3px;
  color: rgba(100, 200, 255, 0.8);
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.3);
}

.garage-header::before {
  content: "⚡";
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28px;
  color: #64c8ff;
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
  animation: pulse 2s ease-in-out infinite;
}

.garage-header::after {
  content: "⚡";
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28px;
  color: #64c8ff;
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
  animation: pulse 2s ease-in-out infinite reverse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

.sidebar-tabs {
  flex: 1;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tab {
  padding: 20px 30px;
  background: transparent;
  border: 1px solid rgba(100, 200, 255, 0.1);
  border-radius: 12px;
  margin: 0 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 10px;
}

.tab-icon {
  font-size: 18px;
  transition: all 0.3s ease;
}

.tab-text {
  font-family: 'Exo 2', sans-serif;
}

.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(100, 200, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.tab:hover {
  background: rgba(100, 200, 255, 0.1);
  border-color: rgba(100, 200, 255, 0.3);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(100, 200, 255, 0.2);
}

.tab:hover .tab-icon {
  transform: scale(1.2);
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.8);
}

.tab:hover::before {
  left: 100%;
}

.tab.active {
  background: linear-gradient(135deg,
    rgba(100, 200, 255, 0.2) 0%,
    rgba(50, 150, 255, 0.15) 100%);
  border-color: rgba(100, 200, 255, 0.5);
  border-left: 4px solid #64c8ff;
  color: #64c8ff;
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
}

.tab.active .tab-icon {
  transform: scale(1.1);
  text-shadow: 0 0 15px rgba(100, 200, 255, 1);
}

.close-btn {
  padding: 25px;
  text-align: center;
  background: linear-gradient(135deg,
    rgba(255, 100, 100, 0.2) 0%,
    rgba(255, 50, 50, 0.1) 100%);
  border: 1px solid rgba(255, 100, 100, 0.3);
  border-radius: 12px 12px 0 0;
  color: #ff6464;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.close-text {
  font-family: 'Exo 2', sans-serif;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
}

.close-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 100, 100, 0.3) 50%,
    transparent 100%);
  transition: left 0.4s ease;
}

.close-btn:hover {
  background: linear-gradient(135deg,
    rgba(255, 100, 100, 0.3) 0%,
    rgba(255, 50, 50, 0.2) 100%);
  transform: scale(1.05);
  text-shadow: 0 0 10px rgba(255, 100, 100, 0.8);
}

.close-btn:hover::before {
  left: 100%;
}

.main-content {
  flex: 1;
  background: linear-gradient(135deg,
    rgba(10, 10, 30, 0.3) 0%,
    rgba(20, 20, 50, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(100, 200, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 100, 200, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(100, 255, 200, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.content-header {
  text-align: center;
  padding: 30px 40px 20px;
  position: relative;
  z-index: 2;
}

.content-title {
  font-family: 'Orbitron', monospace;
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow:
    0 0 20px rgba(100, 200, 255, 0.6),
    0 2px 4px rgba(0, 0, 0, 0.8);
}

.content-subtitle {
  font-family: 'Exo 2', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: rgba(100, 200, 255, 0.8);
  margin: 0;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.3);
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  padding: 40px;
  max-width: 1200px;
  width: 100%;
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 200, 255, 0.3) transparent;
}

.vehicle-card {
  animation: cardSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(50px) rotateX(10deg);
}

.vehicle-card:nth-child(1) { animation-delay: 0.1s; }
.vehicle-card:nth-child(2) { animation-delay: 0.2s; }
.vehicle-card:nth-child(3) { animation-delay: 0.3s; }
.vehicle-card:nth-child(4) { animation-delay: 0.4s; }
.vehicle-card:nth-child(5) { animation-delay: 0.5s; }
.vehicle-card:nth-child(6) { animation-delay: 0.6s; }
.vehicle-card:nth-child(7) { animation-delay: 0.7s; }

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
  }
}

.vehicle-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(100, 200, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(100, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  color: white;
  position: relative;
  overflow: hidden;
  transform: translateY(0) perspective(1000px) rotateX(0deg);
}

.vehicle-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(100, 200, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
  z-index: 1;
}

.vehicle-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(100, 200, 255, 0.05) 0%,
    transparent 50%,
    rgba(255, 100, 200, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.vehicle-card:hover {
  transform: translateY(-15px) perspective(1000px) rotateX(5deg) scale(1.03);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 10px 30px rgba(100, 200, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(100, 200, 255, 0.5);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%);
}

.vehicle-card:hover::before {
  left: 100%;
}

.vehicle-card:hover::after {
  opacity: 1;
}

.vehicle-card.locked {
  background: linear-gradient(135deg,
    rgba(255, 100, 100, 0.1) 0%,
    rgba(255, 50, 50, 0.05) 100%);
  cursor: not-allowed;
  opacity: 0.6;
  border-color: rgba(255, 100, 100, 0.3);
}

.vehicle-card.locked:hover {
  transform: translateY(-5px) scale(1.01);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(255, 100, 100, 0.2);
}

.vehicle-image {
  width: 100%;
  height: 120px;
  margin-bottom: 20px;
  border-radius: 16px;
  background: linear-gradient(135deg,
    rgba(100, 200, 255, 0.1) 0%,
    rgba(50, 150, 255, 0.05) 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  border: 1px solid rgba(100, 200, 255, 0.3);
  overflow: hidden;
  transition: all 0.4s ease;
}

.vehicle-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.vehicle-card:hover .vehicle-image::before {
  transform: translateX(100%);
}

/* Enhanced vehicle-specific styling with premium CSS art */
.vehicle-image::after {
  content: "";
  position: absolute;
  width: 80px;
  height: 35px;
  background: linear-gradient(45deg, #64c8ff, #96d9ff);
  border-radius: 20px 8px 8px 20px;
  box-shadow:
    0 4px 15px rgba(100, 200, 255, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  z-index: 2;
}

.vehicle-image[data-vehicle="adder"]::after {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  border-radius: 25px 5px 5px 25px;
  height: 28px;
  box-shadow:
    0 4px 15px rgba(255, 107, 107, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="zentorno"]::after {
  background: linear-gradient(45deg, #4ecdc4, #7fdbda);
  border-radius: 30px 3px 3px 30px;
  height: 25px;
  box-shadow:
    0 4px 15px rgba(78, 205, 196, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="t20"]::after {
  background: linear-gradient(45deg, #45b7d1, #74c7ec);
  border-radius: 35px 2px 2px 35px;
  height: 22px;
  box-shadow:
    0 4px 15px rgba(69, 183, 209, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="insurgent"]::after {
  background: linear-gradient(45deg, #6c5ce7, #a29bfe);
  border-radius: 12px;
  height: 45px;
  width: 70px;
  box-shadow:
    0 4px 15px rgba(108, 92, 231, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="kuruma2"]::after {
  background: linear-gradient(45deg, #fd79a8, #fdcb6e);
  border-radius: 18px 12px 12px 18px;
  height: 32px;
  box-shadow:
    0 4px 15px rgba(253, 121, 168, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="elegy2"]::after {
  background: linear-gradient(45deg, #00b894, #55efc4);
  border-radius: 22px 10px 10px 22px;
  height: 30px;
  box-shadow:
    0 4px 15px rgba(0, 184, 148, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.vehicle-image[data-vehicle="buzzard2"]::after {
  background: linear-gradient(45deg, #2d3436, #636e72);
  border-radius: 50% 50% 15px 15px;
  height: 40px;
  width: 65px;
  box-shadow:
    0 4px 15px rgba(45, 52, 54, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

.vehicle-image[data-vehicle="buzzard2"]::before {
  content: "";
  position: absolute;
  width: 90px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #ddd, transparent);
  top: 35px;
  animation: rotate 1.5s linear infinite;
  z-index: 3;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.vehicle-name {
  font-family: 'Orbitron', monospace;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
  position: relative;
  z-index: 3;
}

.vehicle-price {
  font-family: 'Exo 2', sans-serif;
  font-size: 20px;
  color: #64ff96;
  margin-bottom: 15px;
  font-weight: 700;
  text-shadow: 0 0 15px rgba(100, 255, 150, 0.6);
  position: relative;
  z-index: 3;
}

.vehicle-price::before {
  content: '$';
  font-size: 16px;
  opacity: 0.8;
  margin-right: 2px;
}

.vehicle-status {
  font-size: 14px;
  padding: 10px 16px;
  border-radius: 25px;
  display: inline-block;
  margin: 10px 0;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 700;
  position: relative;
  z-index: 3;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.status-available {
  background: linear-gradient(135deg,
    rgba(100, 255, 150, 0.3) 0%,
    rgba(50, 255, 100, 0.2) 100%);
  color: #64ff96;
  border: 1px solid rgba(100, 255, 150, 0.5);
  box-shadow:
    0 0 20px rgba(100, 255, 150, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.status-locked {
  background: linear-gradient(135deg,
    rgba(255, 100, 100, 0.3) 0%,
    rgba(255, 50, 50, 0.2) 100%);
  color: #ff6464;
  border: 1px solid rgba(255, 100, 100, 0.5);
  box-shadow:
    0 0 20px rgba(255, 100, 100, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.purchase-btn {
  margin-top: 20px;
  padding: 16px 28px;
  background: linear-gradient(135deg,
    #64ff96 0%,
    #32ff64 50%,
    #00ff32 100%);
  color: #000000;
  border: none;
  border-radius: 30px;
  font-family: 'Exo 2', sans-serif;
  font-weight: 700;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8px 25px rgba(100, 255, 150, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 3;
  overflow: hidden;
}

.purchase-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  transition: left 0.5s ease;
}

.purchase-btn:hover {
  background: linear-gradient(135deg,
    #32ff64 0%,
    #00ff32 50%,
    #00cc28 100%);
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(100, 255, 150, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.purchase-btn:hover::before {
  left: 100%;
}

.purchase-btn:active {
  transform: translateY(-2px) scale(1.02);
}

.locked-btn {
  margin-top: 20px;
  padding: 16px 28px;
  background: linear-gradient(135deg,
    rgba(255, 100, 100, 0.3) 0%,
    rgba(255, 50, 50, 0.2) 100%);
  color: #ff6464;
  border: 1px solid rgba(255, 100, 100, 0.5);
  border-radius: 30px;
  font-family: 'Exo 2', sans-serif;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: not-allowed;
  opacity: 0.8;
  backdrop-filter: blur(10px);
  box-shadow:
    0 5px 15px rgba(255, 100, 100, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 3;
}

.no-vehicles {
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-size: 28px;
  text-align: center;
  opacity: 0.8;
  text-shadow: 0 0 20px rgba(100, 200, 255, 0.5);
  margin: 50px 0;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(100, 200, 255, 0.1);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(100, 200, 255, 0.6) 0%,
    rgba(50, 150, 255, 0.4) 100%);
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(100, 200, 255, 0.8) 0%,
    rgba(50, 150, 255, 0.6) 100%);
}

/* Additional premium effects */
.vehicle-card:hover .vehicle-name {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
  transform: translateY(-2px);
}

.vehicle-card:hover .vehicle-price {
  text-shadow: 0 0 20px rgba(100, 255, 150, 0.8);
  transform: scale(1.05);
}

.vehicle-card:hover .vehicle-status {
  transform: scale(1.1);
}

/* Loading animation for purchase button */
@keyframes purchaseLoading {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.purchase-btn.loading {
  animation: purchaseLoading 0.6s ease-in-out infinite;
  background: linear-gradient(135deg,
    #ffaa64 0%,
    #ff8832 50%,
    #ff6600 100%);
}

/* Floating particles effect */
.garage-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(100, 200, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 100, 200, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(100, 255, 200, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(100, 200, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255, 100, 200, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: float 20s linear infinite;
  pointer-events: none;
  opacity: 0.6;
}

@keyframes float {
  0% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-10px) translateX(10px); }
  66% { transform: translateY(5px) translateX(-5px); }
  100% { transform: translateY(0px) translateX(0px); }
}

/* Responsive design */
@media (max-width: 1200px) {
  .vehicle-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .garage-container {
    width: 98vw;
    height: 95vh;
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 140px;
    flex-direction: row;
    border-right: none;
    border-bottom: 1px solid rgba(100, 200, 255, 0.2);
  }

  .garage-header {
    flex: 1;
    padding: 20px;
  }

  .header-text {
    font-size: 20px;
    margin-bottom: 3px;
  }

  .header-subtext {
    font-size: 10px;
  }

  .sidebar-tabs {
    flex: 2;
    flex-direction: row;
    padding: 10px;
    gap: 8px;
  }

  .tab {
    padding: 15px 20px;
    margin: 0 5px;
    font-size: 12px;
    flex-direction: column;
    gap: 5px;
  }

  .tab-icon {
    font-size: 16px;
  }

  .close-btn {
    flex: 0.5;
    padding: 15px 10px;
    font-size: 16px;
    gap: 3px;
  }

  .close-text {
    font-size: 10px;
  }

  .content-header {
    padding: 20px;
  }

  .content-title {
    font-size: 24px;
  }

  .content-subtitle {
    font-size: 14px;
  }

  .vehicle-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
  }

  .vehicle-card {
    padding: 20px;
  }

  .vehicle-image {
    height: 100px;
  }

  .vehicle-name {
    font-size: 18px;
  }

  .vehicle-price {
    font-size: 16px;
  }
}
